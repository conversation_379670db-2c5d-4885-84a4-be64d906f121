{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/our-story/ourStory.module.css"], "sourcesContent": ["/* Our Story Page Styles - Magazine/Editorial Theme */\n:root {\n  --cast-stone-brown: #4a3728;\n  --cast-stone-light-brown: #6b4e3d;\n  --cast-stone-cream: #faf9f7;\n  --cast-stone-white: #ffffff;\n  --cast-stone-gray: #8b7355;\n  --cast-stone-shadow: rgba(74, 55, 40, 0.1);\n  --cast-stone-shadow-hover: rgba(74, 55, 40, 0.15);\n  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Page Container */\n.storyPage {\n  min-height: 100vh;\n  background-color: var(--cast-stone-white);\n}\n\n/* Hero Section */\n.heroSection {\n  background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);\n  padding: 8rem 0 6rem;\n  position: relative;\n  overflow: hidden;\n}\n\n.heroSection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.02)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  opacity: 0.3;\n}\n\n.heroContainer {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  text-align: center;\n  position: relative;\n  z-index: 2;\n}\n\n.heroTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 4rem;\n  font-weight: 700;\n  color: var(--cast-stone-white);\n  margin-bottom: 1.5rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  letter-spacing: -0.02em;\n  animation: fadeInUp 1s ease-out forwards;\n}\n\n.heroSubtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: rgba(255, 255, 255, 0.9);\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\n  animation: fadeInUp 1s ease-out 0.3s forwards;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n/* Main Content */\n.mainContent {\n  padding: 6rem 0;\n  background-color: var(--cast-stone-white);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* Section Titles */\n.sectionTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--cast-stone-brown);\n  text-align: center;\n  margin-bottom: 3rem;\n  letter-spacing: -0.01em;\n  position: relative;\n}\n\n.sectionTitle::after {\n  content: '';\n  position: absolute;\n  bottom: -1rem;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 80px;\n  height: 3px;\n  background: linear-gradient(90deg, var(--cast-stone-brown), var(--cast-stone-light-brown));\n  border-radius: 2px;\n}\n\n/* Introduction Section */\n.introSection {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 4rem;\n  align-items: center;\n  margin-bottom: 6rem;\n  padding: 4rem 0;\n}\n\n.introContent {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.introText {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.1rem;\n  line-height: 1.8;\n  color: var(--cast-stone-gray);\n  text-align: justify;\n}\n\n.introImage {\n  position: relative;\n}\n\n.imagePlaceholder {\n  width: 100%;\n  height: 400px;\n  background: linear-gradient(135deg, var(--cast-stone-cream), #f0ede8);\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid rgba(74, 55, 40, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.imagePlaceholder::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"texture\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><rect width=\"20\" height=\"20\" fill=\"rgba(74,55,40,0.02)\"/><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(74,55,40,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23texture)\"/></svg>');\n}\n\n.imageText {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.2rem;\n  color: var(--cast-stone-brown);\n  font-weight: 600;\n  position: relative;\n  z-index: 2;\n}\n\n/* Heritage Section */\n.heritageSection {\n  margin-bottom: 6rem;\n  padding: 4rem 0;\n  background: var(--cast-stone-cream);\n  border-radius: 24px;\n}\n\n.heritageGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  margin-top: 3rem;\n}\n\n.heritageCard {\n  background: var(--cast-stone-white);\n  padding: 2.5rem;\n  border-radius: 16px;\n  text-align: center;\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\n  transition: var(--transition-smooth);\n  border: 1px solid rgba(74, 55, 40, 0.08);\n}\n\n.heritageCard:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 16px 40px var(--cast-stone-shadow-hover);\n}\n\n.heritageIcon {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 1.5rem;\n  color: var(--cast-stone-white);\n}\n\n.heritageTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: var(--cast-stone-brown);\n  margin-bottom: 1rem;\n}\n\n.heritageText {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: var(--cast-stone-gray);\n}\n\n/* Timeline Section */\n.timelineSection {\n  margin-bottom: 6rem;\n  padding: 4rem 0;\n}\n\n.timeline {\n  position: relative;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: linear-gradient(180deg, var(--cast-stone-brown), var(--cast-stone-light-brown));\n  transform: translateX(-50%);\n}\n\n.timelineItem {\n  display: flex;\n  align-items: center;\n  margin-bottom: 3rem;\n  position: relative;\n}\n\n.timelineItem:nth-child(odd) {\n  flex-direction: row;\n}\n\n.timelineItem:nth-child(even) {\n  flex-direction: row-reverse;\n}\n\n.timelineYear {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--cast-stone-white);\n  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));\n  padding: 1rem 1.5rem;\n  border-radius: 50px;\n  min-width: 100px;\n  text-align: center;\n  position: relative;\n  z-index: 2;\n  box-shadow: 0 4px 12px var(--cast-stone-shadow);\n}\n\n.timelineContent {\n  flex: 1;\n  background: var(--cast-stone-white);\n  padding: 2rem;\n  border-radius: 16px;\n  margin: 0 2rem;\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\n  border: 1px solid rgba(74, 55, 40, 0.08);\n}\n\n.timelineTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.3rem;\n  font-weight: 600;\n  color: var(--cast-stone-brown);\n  margin-bottom: 0.5rem;\n}\n\n.timelineText {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: var(--cast-stone-gray);\n}\n\n/* Values Section */\n.valuesSection {\n  margin-bottom: 6rem;\n  padding: 4rem 0;\n  background: var(--cast-stone-cream);\n  border-radius: 24px;\n}\n\n.valuesGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-top: 3rem;\n}\n\n.valueCard {\n  background: var(--cast-stone-white);\n  padding: 2rem;\n  border-radius: 16px;\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\n  transition: var(--transition-smooth);\n  border: 1px solid rgba(74, 55, 40, 0.08);\n}\n\n.valueCard:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 32px var(--cast-stone-shadow-hover);\n}\n\n.valueTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 1.3rem;\n  font-weight: 600;\n  color: var(--cast-stone-brown);\n  margin-bottom: 1rem;\n}\n\n.valueText {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: var(--cast-stone-gray);\n}\n\n/* Call to Action Section */\n.ctaSection {\n  text-align: center;\n  padding: 4rem 0;\n  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));\n  border-radius: 24px;\n  color: var(--cast-stone-white);\n  position: relative;\n  overflow: hidden;\n}\n\n.ctaSection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"cta-grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23cta-grain)\"/></svg>');\n  opacity: 0.3;\n}\n\n.ctaContent {\n  position: relative;\n  z-index: 2;\n}\n\n.ctaTitle {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 1rem;\n  letter-spacing: -0.01em;\n}\n\n.ctaText {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.2rem;\n  margin-bottom: 2.5rem;\n  opacity: 0.9;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.ctaButtons {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.primaryButton,\n.secondaryButton {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  padding: 1.25rem 2.5rem;\n  border-radius: 50px;\n  text-decoration: none;\n  transition: var(--transition-smooth);\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  min-width: 200px;\n  text-align: center;\n}\n\n.primaryButton {\n  background: var(--cast-stone-white);\n  color: var(--cast-stone-brown);\n  border: 2px solid var(--cast-stone-white);\n}\n\n.primaryButton:hover {\n  background: transparent;\n  color: var(--cast-stone-white);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.secondaryButton {\n  background: transparent;\n  color: var(--cast-stone-white);\n  border: 2px solid var(--cast-stone-white);\n}\n\n.secondaryButton:hover {\n  background: var(--cast-stone-white);\n  color: var(--cast-stone-brown);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .introSection {\n    grid-template-columns: 1fr;\n    gap: 3rem;\n  }\n  \n  .heroTitle {\n    font-size: 3rem;\n  }\n  \n  .sectionTitle {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .heroSection {\n    padding: 6rem 0 4rem;\n  }\n  \n  .heroTitle {\n    font-size: 2.5rem;\n  }\n  \n  .heroSubtitle {\n    font-size: 1.1rem;\n  }\n  \n  .mainContent {\n    padding: 4rem 0;\n  }\n  \n  .container {\n    padding: 0 1rem;\n  }\n  \n  .timeline::before {\n    left: 2rem;\n  }\n  \n  .timelineItem {\n    flex-direction: row !important;\n    padding-left: 4rem;\n  }\n  \n  .timelineYear {\n    position: absolute;\n    left: 0;\n    min-width: 80px;\n    font-size: 1.2rem;\n    padding: 0.75rem 1rem;\n  }\n  \n  .timelineContent {\n    margin-left: 2rem;\n    margin-right: 0;\n  }\n  \n  .ctaButtons {\n    flex-direction: column;\n    align-items: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroTitle {\n    font-size: 2rem;\n  }\n  \n  .heroSubtitle {\n    font-size: 1rem;\n  }\n  \n  .sectionTitle {\n    font-size: 1.75rem;\n  }\n  \n  .ctaTitle {\n    font-size: 2rem;\n  }\n  \n  .heritageCard,\n  .valueCard,\n  .timelineContent {\n    padding: 1.5rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;;EAQA;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}