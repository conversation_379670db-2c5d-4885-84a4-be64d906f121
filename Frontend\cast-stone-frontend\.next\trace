[{"name": "hot-reloader", "duration": 66, "timestamp": 265864809027, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751805570989, "traceId": "6a575780b9fd724d"}, {"name": "setup-dev-bundler", "duration": 570791, "timestamp": 265864634183, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751805570814, "traceId": "6a575780b9fd724d"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 265865252777, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751805571433, "traceId": "6a575780b9fd724d"}, {"name": "start-dev-server", "duration": 1077422, "timestamp": 265864207504, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "15040217088", "memory.totalMem": "33974333440", "memory.heapSizeLimit": "17037262848", "memory.rss": "187650048", "memory.heapTotal": "102395904", "memory.heapUsed": "64177920"}, "startTime": 1751805570387, "traceId": "6a575780b9fd724d"}, {"name": "compile-path", "duration": 1665726, "timestamp": 265873363892, "id": 7, "tags": {"trigger": "/our-story"}, "startTime": 1751805579544, "traceId": "6a575780b9fd724d"}, {"name": "ensure-page", "duration": 1667200, "timestamp": 265873363252, "id": 6, "parentId": 3, "tags": {"inputPage": "/our-story/page"}, "startTime": 1751805579543, "traceId": "6a575780b9fd724d"}]