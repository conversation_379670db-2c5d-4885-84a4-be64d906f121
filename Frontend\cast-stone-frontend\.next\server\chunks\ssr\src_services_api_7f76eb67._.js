module.exports = {

"[project]/src/services/api/products/update.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/api/products/get.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/products/get.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/api/users/get.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/api/users/update.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript)");
    });
});
}}),

};