{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/admin/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\n\nexport default function AdminLoginPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  \n  const { login, isAuthenticated, isLoading } = useAdminAuth();\n  const router = useRouter();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      router.push('/admin/dashboard');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsSubmitting(true);\n\n    try {\n      const success = await login(email, password);\n      \n      if (success) {\n        router.push('/admin/dashboard');\n      } else {\n        setError('Invalid email or password. Please check your credentials.');\n      }\n    } catch (error) {\n      setError('An error occurred during login. Please try again.');\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-white\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto\"></div>\n          <p className=\"mt-4 text-amber-900\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-white\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-amber-900 mb-2\">Cast Stone</h1>\n          <h2 className=\"text-2xl font-semibold text-amber-800\">Admin Portal</h2>\n          <p className=\"mt-2 text-sm text-amber-700\">\n            Sign in to access the admin dashboard\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-amber-900\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-amber-300 rounded-md shadow-sm placeholder-amber-400 focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-amber-900\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"current-password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-amber-300 rounded-md shadow-sm placeholder-amber-400 focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-amber-900 hover:bg-amber-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Signing in...\n                </div>\n              ) : (\n                'Sign in'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-xs text-amber-600\">\n              For demo purposes: <EMAIL> / 132Trent@!\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,mBAAmB,CAAC,WAAW;gBACjC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,MAAM,UAAU,MAAM,MAAM,OAAO;YAEnC,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;YACT,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI3C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAK7C,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA2C;;;;;;sDAG5E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA2C;;;;;;sDAG/E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;wBAKjB,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,6BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;sCAKN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;GAhIwB;;QAMwB,uIAAA,CAAA,eAAY;QAC3C,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}